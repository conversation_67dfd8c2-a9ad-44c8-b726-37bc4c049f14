"use client";

import React, { useState, useEffect } from 'react';
import { BaseModal } from '@/shared/UI/modals';
import { PrimaryButton } from '@/shared/UI/buttons';
import { useQRScanner, extractBetIdFromQR, isValidBetId } from '@/shared/hooks/business/useQRScanner';
import { useBetWinReportQuery } from '@/shared/query/useBetWinReportQuery';
import { useBetSettlement } from '@/shared/hooks/business/useBetSettlement';
import { BetReportData } from '@/shared/types/report-types';
import BetSettlementModal, { BetSettlementData } from './BetSettlementModal';

interface CheckResultModalProps {
  isOpen: boolean;
  onClose: () => void;
}

/**
 * Helper function to map numeric payoutStatus to string
 */
const mapPayoutStatus = (payoutStatus: any): string => {
  if (typeof payoutStatus === 'number') {
    switch (payoutStatus) {
      case 1: return 'settled';
      case 2: return 'rejected';
      case 3: return 'unsettled';
      default: return 'unsettled';
    }
  }
  return typeof payoutStatus === 'string' ? payoutStatus.toLowerCase() : 'unsettled';
};

/**
 * Utility function to format bet result data for display
 */
const formatBetResultForDisplay = (betResult: BetReportData) => {
  const date = new Date(betResult.createdAt);
  const formattedDate = date.toLocaleDateString('en-GB', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
  }).replace(/\//g, ' / ');

  const formattedTime = date.toLocaleTimeString('en-GB', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false,
  });

  return {
    ...betResult,
    formattedDate,
    formattedTime,
    displayAmount: betResult.status === 'win' ? betResult.winAmount : betResult.betAmount,
  };
};

/**
 * CheckResultModal Component
 * 
 * A modal for checking bet results via QR code scanning or manual bet ID input.
 * Features:
 * - 400px height modal
 * - QR scanner functionality with camera access
 * - Manual bet ID input
 * - Results display with win/loss status
 * - Scrollable bet list
 * - Mark as settled functionality
 */
const CheckResultModal: React.FC<CheckResultModalProps> = ({
  isOpen,
  onClose,
}) => {
  const [currentState, setCurrentState] = useState<'input' | 'results'>('input');
  const [betId, setBetId] = useState('');
  const [searchBetId, setSearchBetId] = useState('');
  const [error, setError] = useState<string | null>(null);

  // QR Scanner hook
  const { isScanning, error: scanError, videoRef, startScanning, stopScanning } = useQRScanner({
    onScanSuccess: (qrData) => {
      const extractedBetId = extractBetIdFromQR(qrData);
      if (extractedBetId && isValidBetId(extractedBetId)) {
        setBetId(extractedBetId);
        setSearchBetId(extractedBetId);
        stopScanning();
      } else {
        setError('Invalid QR code format. Please scan a valid bet QR code.');
      }
    },
    onScanError: (scanErrorMsg) => {
      setError(scanErrorMsg);
    },
  });

  // Bet win report query (using betId as transactionId filter)
  const { data: betWinReportResponse, isLoading, error: queryError } = useBetWinReportQuery(
    {
      transactionId: searchBetId, // Use betId as transactionId filter
      page: 1,
      limit: 1, // Only need one result
      startDate: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString().slice(0, 16), // 1 year ago
      endDate: new Date().toISOString().slice(0, 16), // Now
    },
    !!searchBetId && currentState === 'results' ? undefined : null
  );

  // Bet settlement functionality
  const [isSettlementModalOpen, setIsSettlementModalOpen] = useState(false);
  const [selectedBetData, setSelectedBetData] = useState<BetReportData | null>(null);

  // Use bet settlement hook
  const { settleBet, isSettling } = useBetSettlement({
    onSuccess: () => {
      setIsSettlementModalOpen(false);
      // Optionally refetch the data or show success message
    },
    onError: (error) => {
      setError(error.message);
    }
  });

  // Handle QR scanner initialization
  const handleQRScannerClick = async () => {
    setError(null);
    try {
      await startScanning();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to start QR scanner');
    }
  };

  // Handle search for bet details
  const handleSearch = async () => {
    if (!betId.trim()) {
      setError('Please enter a bet ID');
      return;
    }

    if (!isValidBetId(betId)) {
      setError('Invalid bet ID format');
      return;
    }

    setError(null);
    setSearchBetId(betId);
    setCurrentState('results');
  };

  // Handle settlement button click
  const handleSettlementClick = () => {
    if (!betResult) return;
    setSelectedBetData(betResult);
    setIsSettlementModalOpen(true);
  };

  // Handle settlement submission
  const handleSettlementSubmit = async (settlementData: BetSettlementData) => {
    if (!selectedBetData?.betId) return;

    try {
      await settleBet(selectedBetData.betId, settlementData);
    } catch {
      // Error is handled by the hook
    }
  };

  // Handle share/copy
  const handleShare = () => {
    if (betResult?.betId) {
      navigator.clipboard.writeText(`Bet ID: ${betResult.betId}`);
    }
  };

  // Reset modal state when closed
  useEffect(() => {
    if (!isOpen) {
      setCurrentState('input');
      setBetId('');
      setSearchBetId('');
      setError(null);
      stopScanning();
    }
  }, [isOpen, stopScanning]);

  // Handle errors from API or scanning
  useEffect(() => {
    if (queryError) {
      setError(queryError instanceof Error ? queryError.message : 'Failed to fetch bet details');
    } else if (scanError) {
      setError(scanError);
    }
  }, [queryError, scanError]);

  // Get the raw bet data from betWinReport response
  const rawBetData = betWinReportResponse?.data?.[0];

  // Get formatted bet result data from betWinReport response
  const betResult = rawBetData ? formatBetResultForDisplay(rawBetData) : null;

  // Create header icon
  const headerIcon = (
    <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="40" height="40" rx="4.57143" fill="#00FFF2" fill-opacity="0.2" />
      <path d="M20.6562 15.4062H16.0625C15.6996 15.4062 15.4062 15.1122 15.4062 14.75C15.4062 14.3877 15.6996 14.0938 16.0625 14.0938H20.6562C21.0192 14.0938 21.3125 14.3877 21.3125 14.75C21.3125 15.1122 21.0192 15.4062 20.6562 15.4062Z" fill="#00FFF2" />
      <path d="M22.625 18.6875H16.0625C15.6996 18.6875 15.4062 18.3935 15.4062 18.0312C15.4062 17.669 15.6996 17.375 16.0625 17.375H22.625C22.9879 17.375 23.2812 17.669 23.2812 18.0312C23.2812 18.3935 22.9879 18.6875 22.625 18.6875Z" fill="#00FFF2" />
      <path d="M19.9081 21.3125H16.0625C15.6996 21.3125 15.4062 21.0185 15.4062 20.6562C15.4062 20.294 15.6996 20 16.0625 20H19.9081C20.271 20 20.5644 20.294 20.5644 20.6562C20.5644 21.0185 20.271 21.3125 19.9081 21.3125Z" fill="#00FFF2" />
      <path d="M18.3331 23.9375H16.0625C15.6996 23.9375 15.4062 23.6435 15.4062 23.2812C15.4062 22.919 15.6996 22.625 16.0625 22.625H18.3331C18.696 22.625 18.9894 22.919 18.9894 23.2812C18.9894 23.6435 18.696 23.9375 18.3331 23.9375Z" fill="#00FFF2" />
      <path d="M18.3331 27.875H15.4062C13.597 27.875 12.125 26.403 12.125 24.5938V13.4375C12.125 11.6282 13.597 10.1562 15.4062 10.1562H23.2812C25.0905 10.1562 26.5625 11.6282 26.5625 13.4375V18.8187C26.5625 19.181 26.2692 19.475 25.9062 19.475C25.5433 19.475 25.25 19.181 25.25 18.8187V13.4375C25.25 12.3521 24.3667 11.4688 23.2812 11.4688H15.4062C14.3208 11.4688 13.4375 12.3521 13.4375 13.4375V24.5938C13.4375 25.6792 14.3208 26.5625 15.4062 26.5625H18.3331C18.696 26.5625 18.9894 26.8565 18.9894 27.2188C18.9894 27.581 18.696 27.875 18.3331 27.875Z" fill="#00FFF2" />
      <path d="M24.5938 29.8438C22.0613 29.8438 20 27.7831 20 25.25C20 22.7169 22.0613 20.6562 24.5938 20.6562C27.1262 20.6562 29.1875 22.7169 29.1875 25.25C29.1875 27.7831 27.1262 29.8438 24.5938 29.8438ZM24.5938 21.9688C22.7845 21.9688 21.3125 23.4407 21.3125 25.25C21.3125 27.0593 22.7845 28.5312 24.5938 28.5312C26.403 28.5312 27.875 27.0593 27.875 25.25C27.875 23.4407 26.403 21.9688 24.5938 21.9688Z" fill="#00FFF2" />
      <path d="M24.1562 26.9123C23.9882 26.9123 23.8202 26.848 23.6922 26.72L22.8174 25.8453C22.5609 25.5887 22.5609 25.1739 22.8174 24.9173C23.074 24.6607 23.4888 24.6607 23.7454 24.9173L24.1562 25.3281L25.4424 24.0425C25.699 23.7859 26.1138 23.7859 26.3704 24.0425C26.627 24.2991 26.627 24.7145 26.3704 24.9705L24.6202 26.7207C24.4922 26.8487 24.3242 26.9123 24.1562 26.9123Z" fill="#00FFF2" />
    </svg>

  );

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={onClose}
      title="Bet Result"
      headerIcon={headerIcon}
      size="md"
      bodyClassName={`flex flex-col h-[${currentState === 'input' ? '100%' : '550px'}]`}
      width={` ${currentState === 'input' ? '460px' : '776px'}`}
      maxHeight="550px"
      className="check-result-modal"
    >
      <>
        {currentState === 'input' ? (
          // Initial State - Input Section
          <div className="flex-1 flex flex-col justify-center items-start gap-6 p-6">
            {/* QR Scanner Section */}
            <div className="flex items-center gap-6 w-full">
              {/* QR Scanner */}
              <div className="flex flex-col items-center gap-2">
                <div
                  className="w-[70px] h-[70px] bg-[#B0822A] rounded-lg flex items-center justify-center cursor-pointer relative overflow-hidden"
                  onClick={handleQRScannerClick}
                >
                  {isScanning ? (
                    <video
                      ref={videoRef}
                      className="w-full h-full object-cover"
                      autoPlay
                      muted
                      playsInline
                    />
                  ) : (
                    <svg width="42" height="42" viewBox="0 0 42 42" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-white">
                      <g clipPath="url(#clip0_822_4602)">
                        <path d="M2.32469 15.4948C1.19591 15.4948 0.443359 14.7423 0.443359 13.6135V6.08825C0.443359 2.89002 2.88904 0.444336 6.08727 0.444336H13.6125C14.7413 0.444336 15.4938 1.19688 15.4938 2.32567C15.4938 3.45445 14.7413 4.207 13.6125 4.207H6.08727C4.95849 4.207 4.20594 4.95954 4.20594 6.08833V13.6136C4.20602 14.7423 3.45347 15.4948 2.32469 15.4948Z" fill="white" />
                        <path d="M39.9505 15.4948C38.8217 15.4948 38.0692 14.7423 38.0692 13.6135V6.08825C38.0692 4.95947 37.3166 4.20692 36.1878 4.20692H28.6626C27.5338 4.20692 26.7812 3.45437 26.7812 2.32559C26.7812 1.19681 27.5338 0.444336 28.6626 0.444336H36.1878C39.3861 0.444336 41.8317 2.89002 41.8317 6.08825V13.6135C41.8318 14.7423 41.0793 15.4948 39.9505 15.4948Z" fill="white" />
                        <path d="M13.6125 41.8332H6.08727C2.88904 41.8332 0.443359 39.3875 0.443359 36.1893V28.664C0.443359 27.5353 1.19591 26.7827 2.32469 26.7827C3.45347 26.7827 4.20602 27.5353 4.20602 28.664V36.1893C4.20602 37.3181 4.95857 38.0706 6.08735 38.0706H13.6126C14.7414 38.0706 15.4939 38.8232 15.4939 39.952C15.4938 41.0807 14.7413 41.8332 13.6125 41.8332Z" fill="white" />
                        <path d="M36.1888 41.8333H28.6636C27.5348 41.8333 26.7822 41.0807 26.7822 39.952C26.7822 38.8232 27.5348 38.0706 28.6636 38.0706H36.1888C37.3176 38.0706 38.0701 37.3181 38.0701 36.1893V28.664C38.0701 27.5353 38.8227 26.7827 39.9515 26.7827C41.0802 26.7827 41.8328 27.5353 41.8328 28.664V36.1893C41.8327 39.3876 39.387 41.8333 36.1888 41.8333Z" fill="white" />
                        <path d="M17.3753 19.2574H9.85008C8.7213 19.2574 7.96875 18.5048 7.96875 17.3761V9.85081C7.96875 8.72203 8.7213 7.96948 9.85008 7.96948H17.3753C18.5041 7.96948 19.2567 8.72203 19.2567 9.85081V17.3761C19.2567 18.5048 18.5041 19.2574 17.3753 19.2574ZM11.7314 15.4947H15.4941V11.7321H11.7314V15.4947Z" fill="white" />
                        <path d="M17.3753 34.3079H9.85008C8.7213 34.3079 7.96875 33.5554 7.96875 32.4266V24.9013C7.96875 23.7726 8.7213 23.02 9.85008 23.02H17.3753C18.5041 23.02 19.2567 23.7726 19.2567 24.9013V32.4266C19.2567 33.5554 18.5041 34.3079 17.3753 34.3079ZM11.7314 30.5453H15.4941V26.7826H11.7314V30.5453Z" fill="white" />
                        <path d="M32.4261 19.2574H24.9009C23.7721 19.2574 23.0195 18.5048 23.0195 17.3761V9.85081C23.0195 8.72203 23.7721 7.96948 24.9009 7.96948H32.4261C33.5549 7.96948 34.3074 8.72203 34.3074 9.85081V17.3761C34.3074 18.5048 33.5549 19.2574 32.4261 19.2574ZM26.7822 15.4947H30.5449V11.7321H26.7822V15.4947Z" fill="white" />
                        <path d="M24.9009 28.664C23.7721 28.664 23.0195 27.9115 23.0195 26.7827V24.9013C23.0195 23.7726 23.7721 23.02 24.9009 23.02C26.0296 23.02 26.7822 23.7726 26.7822 24.9013V26.7827C26.7822 27.9115 26.0296 28.664 24.9009 28.664Z" fill="white" />
                        <path d="M32.4257 26.7827H30.5444C29.4156 26.7827 28.6631 26.0301 28.6631 24.9013C28.6631 23.7726 29.4156 23.02 30.5444 23.02H32.4257C33.5545 23.02 34.3071 23.7726 34.3071 24.9013C34.3071 26.0301 33.5545 26.7827 32.4257 26.7827Z" fill="white" />
                        <path d="M32.4256 34.3081H26.7817C25.6529 34.3081 24.9004 33.5555 24.9004 32.4267C24.9004 31.298 25.6529 30.5454 26.7817 30.5454H32.4256C33.5544 30.5454 34.307 31.298 34.307 32.4267C34.307 33.5555 33.5544 34.3081 32.4256 34.3081Z" fill="white" />
                      </g>
                      <defs>
                        <clipPath id="clip0_822_4602">
                          <rect width="42" height="42" fill="white" />
                        </clipPath>
                      </defs>
                    </svg>
                  )}
                </div>
                <span className="text-sm font-rubik text-white">Scan QR</span>
              </div>

              {/* OR Divider */}
              <div className="flex flex-col items-center gap-4">
                <div className="h-[20px] w-px bg-gray-600"></div>
                <span className="text-gray-400 font-rubik text-sm">OR</span>
                <div className="h-[20px] w-px bg-gray-600 "></div>
              </div>

              {/* Input Section */}
              <div className="flex flex-col gap-3 flex-1 w-full">
                <div className="flex flex-col gap-[8px]">
                  <label
                    className="font-rubik font-semibold text-white capitalize"
                    style={{ fontSize: '14px', fontWeight: 600 }}
                  >
                    Bet ID
                  </label>
                  <input
                    type="text"
                    className={`w-full h-[43px] bg-elevated  rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-400 focus:border-transparent `}
                    placeholder="Enter bet ID"
                    value={betId}
                    onChange={(e) => setBetId(e.target.value)}
                  />

                </div>
              </div>
            </div>

            <PrimaryButton
              onClick={handleSearch}
              loading={isLoading}
              size="lg"
              fullWidth
            >
              Search
            </PrimaryButton>

            {/* Error Display */}
            {error && (
              <div className="text-red-500 text-sm font-rubik text-center">
                {error}
              </div>
            )}
          </div>
        ) : (
          // Results State
          <div className="flex flex-col h-full">
            {/* Section 1 - Header */}
            <div className="p-4 border-b border-[#AF9660]">
              <div className="flex items-center justify-between">
                <div className="flex flex-col">
                  <span className="text-white font-rubik text-sm">
                    {betResult?.formattedDate} . {betResult?.formattedTime}
                  </span>
                  <span className="text-gray-400 font-rubik text-sm">
                    Bet ID: {betResult?.betId}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  {/* Share/Copy Buttons */}
                  <button
                    onClick={handleShare}
                    className="w-[78px] h-[46px] flex gap-1 px-2 items-center rounded-[8px] bg-[#333335] rounded  text-secondary font-rubik text-sm"
                  >
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12.3649 1.33325H6.30107C5.03221 1.33325 3.99967 2.36579 3.99967 3.63465V3.99992H3.63441C2.36554 3.99992 1.33301 5.03245 1.33301 6.30132V12.3651C1.33301 13.6341 2.36554 14.6666 3.63441 14.6666H9.69821C10.8639 14.6666 11.8197 13.7921 11.9691 12.6666H12.3649C13.6338 12.6666 14.6663 11.6341 14.6663 10.3652V3.63465C14.6663 2.36579 13.6338 1.33325 12.3649 1.33325ZM13.333 10.3652C13.333 10.899 12.8987 11.3333 12.3649 11.3333H11.9997V6.30132C11.9997 5.03245 10.9671 3.99992 9.69827 3.99992H5.33301V3.63465C5.33301 3.10085 5.76727 2.66659 6.30107 2.66659H12.3649C12.8987 2.66659 13.333 3.10085 13.333 3.63465V10.3652Z" fill="#7B7B7B" />
                    </svg>

                    <span>

                      Copy
                    </span>
                  </button>
                  <button
                    onClick={handleShare}
                    className="w-[78px] h-[46px] flex gap-1 px-2 items-center rounded-[8px] bg-[#333335] rounded  text-secondary font-rubik text-sm"
                  >
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M13.9997 8.66659C13.8229 8.66659 13.6533 8.73682 13.5283 8.86185C13.4032 8.98687 13.333 9.15644 13.333 9.33325V11.3333C13.333 11.8637 13.1223 12.3724 12.7472 12.7475C12.3721 13.1225 11.8634 13.3333 11.333 13.3333H4.66634C4.13591 13.3333 3.6272 13.1225 3.25213 12.7475C2.87705 12.3724 2.66634 11.8637 2.66634 11.3333V4.66659C2.66634 4.13615 2.87705 3.62744 3.25213 3.25237C3.6272 2.8773 4.13591 2.66659 4.66634 2.66659H7.99967C8.17649 2.66659 8.34606 2.59635 8.47108 2.47132C8.5961 2.3463 8.66634 2.17673 8.66634 1.99992C8.66634 1.82311 8.5961 1.65354 8.47108 1.52851C8.34606 1.40349 8.17649 1.33325 7.99967 1.33325H4.66634C3.78261 1.33431 2.93538 1.68584 2.31049 2.31073C1.6856 2.93562 1.33407 3.78286 1.33301 4.66659V11.3333C1.33407 12.217 1.6856 13.0642 2.31049 13.6891C2.93538 14.314 3.78261 14.6655 4.66634 14.6666H11.333C12.2167 14.6655 13.064 14.314 13.6889 13.6891C14.3138 13.0642 14.6653 12.217 14.6663 11.3333V9.33325C14.6663 9.15644 14.5961 8.98687 14.4711 8.86185C14.3461 8.73682 14.1765 8.66659 13.9997 8.66659Z" fill="#7B7B7B" />
                      <path d="M4.67297 8.76134C4.69316 8.90194 4.75773 9.03241 4.85726 9.13374C4.9568 9.23507 5.08609 9.30197 5.22631 9.32467C5.26156 9.33051 5.29724 9.33341 5.33297 9.33334C5.45686 9.3334 5.57831 9.29893 5.68371 9.23381C5.7891 9.16869 5.87426 9.07549 5.92964 8.96467C6.75697 7.30934 8.95831 7.21001 9.99964 7.27201V8.66667C9.99967 8.79851 10.0388 8.92737 10.112 9.03698C10.1853 9.14658 10.2894 9.23201 10.4112 9.28246C10.533 9.33291 10.667 9.34611 10.7963 9.3204C10.9256 9.29468 11.0444 9.23121 11.1376 9.13801L14.471 5.80467C14.5373 5.73839 14.5889 5.65887 14.6224 5.5713C14.6559 5.48373 14.6705 5.39007 14.6654 5.29645C14.6602 5.20284 14.6353 5.11136 14.5924 5.028C14.5495 4.94464 14.4895 4.87126 14.4163 4.81267L11.083 2.14601C10.9849 2.06749 10.8667 2.01828 10.7418 2.00405C10.617 1.98982 10.4907 2.01114 10.3775 2.06555C10.2643 2.11997 10.1688 2.20527 10.1019 2.31162C10.035 2.41797 9.99959 2.54105 9.99964 2.66667V3.94534C8.13297 3.85801 6.70631 4.30201 5.77231 5.26467C5.35308 5.73718 5.03843 6.29293 4.84897 6.89553C4.65952 7.49812 4.59954 8.13395 4.67297 8.76134Z" fill="#7B7B7B" />
                    </svg>

                    <span>
                      Share
                    </span>
                  </button>
                  {/* Win/Loss Badge */}
                  <div className={`w-[78px] h-[46px] rounded-[8px] flex items-center justify-center font-rubik font-semibold text-sm ${betResult?.status === 'win'
                    ? 'bg-[#00B800] text-white'
                    : betResult?.status === 'loss'
                      ? 'bg-red-600 text-white'
                      : 'bg-yellow-600 text-white'
                    }`}>
                    {betResult?.status === 'win' ? 'WIN' : betResult?.status === 'loss' ? 'LOSS' : betResult?.status?.toUpperCase()}
                  </div>
                </div>
              </div>
            </div>

            {/* Section 2 - Scrollable Bet List */}
            <div className="flex-1 overflow-y-auto p-4">
              {rawBetData?.betList?.map((bet: any, index: number) => {
                // Parse market format: "Football -> Brazil. Serie A -> Ceara CE vs Corinthians -> Winner. 1-st half"
                const parts = bet.market.split(' -> ');
                const sport = parts[0] || '';
                const league = parts[1] || '';
                const marketType = parts[3] || '';

                return (
                  <div key={index} className="flex justify-between items-center mb-4 border-b border-gray-700 pb-2">
                    <div className="flex flex-col gap-1 flex-1 items-start">
                      <div className="text-secondary flex gap-1 items-center center  font-rubik font-normal mb-1 text-sm leading-none">
                        <svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <g clip-path="url(#clip0_847_3638)">
                            <path d="M1.75 14.5C2.7165 14.5 3.5 13.7165 3.5 12.75C3.5 11.7835 2.7165 11 1.75 11C0.783502 11 0 11.7835 0 12.75C0 13.7165 0.783502 14.5 1.75 14.5Z" fill="#7B7B7B" />
                            <path d="M10.6057 4.99768C10.2055 3.8631 9.59825 2.85102 8.89533 2.1481C7.92875 1.18152 6.56258 0.583601 5.148 0.507768C3.65875 0.431935 2.32525 0.913185 1.36917 1.86985C-0.561667 3.80127 -0.431001 7.31819 1.648 9.3966C2.35033 10.0989 3.36183 10.7062 4.49758 11.1064C4.52908 11.1174 4.56175 11.1227 4.59442 11.1227C4.67025 11.1227 4.74492 11.0929 4.80092 11.0375L10.5374 5.30102C10.6156 5.2211 10.6424 5.10385 10.6057 4.99768Z" fill="#7B7B7B" />
                            <path d="M13.8979 12.2373L10.7118 9.50612C11.0903 8.64396 11.1784 7.55779 10.9649 6.34679C10.9463 6.23946 10.8693 6.15196 10.7654 6.11929C10.6598 6.08662 10.5478 6.11521 10.4714 6.19162L5.69101 10.9715C5.61401 11.0485 5.58601 11.1616 5.61926 11.2655C5.65193 11.3693 5.73943 11.4463 5.84676 11.465C7.05601 11.679 8.14335 11.591 9.0061 11.2124L11.7373 14.3985C11.7938 14.4645 11.8749 14.5 11.9583 14.5C12.0027 14.5 12.047 14.4901 12.089 14.4691C12.8975 14.0649 13.5648 13.3975 13.9697 12.5885C14.0292 12.4689 14 12.3236 13.8979 12.2373Z" fill="#7B7B7B" />
                          </g>
                          <defs>
                            <clipPath id="clip0_847_3638">
                              <rect width="14" height="14" fill="white" transform="translate(0 0.5)" />
                            </clipPath>
                          </defs>
                        </svg>
                        <span>
                          {bet.match} ({sport})
                        </span>
                      </div>
                      <div className="text-golden font-rubik font-medium text-lg leading-none capitalize">
                        {league}
                      </div>
                      <div className="text-secondary font-normal text-md leading-none text-center">
                        {marketType}
                      </div>
                    </div>
                    <div className="text-golden font-rubik font-semibold text-xl leading-none text-center uppercase">
                      {bet.price}
                    </div>
                  </div>
                );
              })}
            </div>

            {/* Section 3 - Fixed Bottom */}
            <div className="bg-[#FFFFFF1A] rounded-lg p-4 m-4 flex flex-col gap-3">
              <div className="flex justify-between items-center">
                <div className="flex flex-col">
                  <span className="text-gray-400 font-rubik text-sm">Bet Amount</span>
                  <span className="text-white font-rubik font-semibold text-lg">
                    LKR {betResult?.betAmount?.toLocaleString()}
                  </span>
                </div>
                <div className="flex flex-col text-right">
                  <span className="text-gray-400 font-rubik text-sm">
                    {betResult?.status === 'win' ? 'Winning Amount' : 'Losing Amount'}
                  </span>
                  <span className={`font-rubik font-semibold text-lg ${betResult?.status === 'win' ? 'text-green-500' : 'text-red-500'
                    }`}>
                    LKR {(betResult?.status === 'win' ? betResult?.winAmount : betResult?.betAmount)?.toLocaleString()}
                  </span>
                </div>
              </div>

              {/* Settlement Button - Show based on payoutStatus */}
              {betResult && (() => {
                const payoutStatus = mapPayoutStatus(betResult.payoutStatus);
                const isSettled = payoutStatus === 'settled';
                const isRejected = payoutStatus === 'rejected';

                if (isSettled) {
                  return (
                    <div className="text-center py-2">
                      <span className="text-green-500 font-rubik font-semibold">Settled</span>
                    </div>
                  );
                } else if (isRejected) {
                  return (
                    <div className="text-center py-2">
                      <span className="text-red-500 font-rubik font-semibold">Rejected</span>
                    </div>
                  );
                } else {
                  return (
                    <PrimaryButton
                      onClick={handleSettlementClick}
                      loading={isSettling}
                      fullWidth
                    >
                      Mark as Settled
                    </PrimaryButton>
                  );
                }
              })()}
            </div>
          </div>
        )}
      </>

      {/* Bet Settlement Modal */}
      <BetSettlementModal
        isOpen={isSettlementModalOpen}
        onClose={() => setIsSettlementModalOpen(false)}
        onSubmit={handleSettlementSubmit}
        betId={selectedBetData?.betId || ''}
        isLoading={isSettling}
      />
    </BaseModal>
  );
};

export default CheckResultModal;
