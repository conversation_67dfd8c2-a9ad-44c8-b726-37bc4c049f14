"use client";

import React, { useState, useEffect } from 'react';
import { BaseModal } from '@/shared/UI/modals';
import { PrimaryButton } from '@/shared/UI/buttons';
import { useQRScanner, extractBetIdFromQR, isValidBetId } from '@/shared/hooks/business/useQRScanner';
import { useBetResultQuery, useMarkBetAsSettledMutation, formatBetResultForDisplay } from '@/shared/query/useBetResultQuery';

interface CheckResultModalProps {
  isOpen: boolean;
  onClose: () => void;
}

// Remove the local BetResult interface as we're using the one from the API

/**
 * CheckResultModal Component
 * 
 * A modal for checking bet results via QR code scanning or manual bet ID input.
 * Features:
 * - 400px height modal
 * - QR scanner functionality with camera access
 * - Manual bet ID input
 * - Results display with win/loss status
 * - Scrollable bet list
 * - Mark as settled functionality
 */
const CheckResultModal: React.FC<CheckResultModalProps> = ({
  isOpen,
  onClose,
}) => {
  const [currentState, setCurrentState] = useState<'input' | 'results'>('input');
  const [betId, setBetId] = useState('');
  const [searchBetId, setSearchBetId] = useState('');
  const [error, setError] = useState<string | null>(null);

  // QR Scanner hook
  const { isScanning, error: scanError, videoRef, startScanning, stopScanning } = useQRScanner({
    onScanSuccess: (qrData) => {
      const extractedBetId = extractBetIdFromQR(qrData);
      if (extractedBetId && isValidBetId(extractedBetId)) {
        setBetId(extractedBetId);
        setSearchBetId(extractedBetId);
        stopScanning();
      } else {
        setError('Invalid QR code format. Please scan a valid bet QR code.');
      }
    },
    onScanError: (scanErrorMsg) => {
      setError(scanErrorMsg);
    },
  });

  // Bet result query
  const { data: betResultResponse, isLoading, error: queryError } = useBetResultQuery(
    searchBetId,
    !!searchBetId && currentState === 'results'
  );

  // Mark as settled mutation
  const markAsSettledMutation = useMarkBetAsSettledMutation();

  // Handle QR scanner initialization
  const handleQRScannerClick = async () => {
    setError(null);
    try {
      await startScanning();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to start QR scanner');
    }
  };

  // Handle search for bet details
  const handleSearch = async () => {
    if (!betId.trim()) {
      setError('Please enter a bet ID');
      return;
    }

    if (!isValidBetId(betId)) {
      setError('Invalid bet ID format');
      return;
    }

    setError(null);
    setSearchBetId(betId);
    setCurrentState('results');
  };

  // Handle mark as settled
  const handleMarkAsSettled = async () => {
    if (!searchBetId) return;

    try {
      await markAsSettledMutation.mutateAsync(searchBetId);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to mark as settled');
    }
  };

  // Handle share/copy
  const handleShare = () => {
    if (betResultResponse?.data) {
      navigator.clipboard.writeText(`Bet ID: ${betResultResponse.data.betId}`);
    }
  };

  // Reset modal state when closed
  useEffect(() => {
    if (!isOpen) {
      setCurrentState('input');
      setBetId('');
      setSearchBetId('');
      setError(null);
      stopScanning();
    }
  }, [isOpen, stopScanning]);

  // Handle errors from API or scanning
  useEffect(() => {
    if (queryError) {
      setError(queryError instanceof Error ? queryError.message : 'Failed to fetch bet details');
    } else if (scanError) {
      setError(scanError);
    }
  }, [queryError, scanError]);

  // Get formatted bet result data
  const betResult = betResultResponse?.data ? formatBetResultForDisplay(betResultResponse.data) : null;

  // Create header icon
  const headerIcon = (
    <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="40" height="40" rx="4.57143" fill="#00FFF2" fill-opacity="0.2" />
      <path d="M20.6562 15.4062H16.0625C15.6996 15.4062 15.4062 15.1122 15.4062 14.75C15.4062 14.3877 15.6996 14.0938 16.0625 14.0938H20.6562C21.0192 14.0938 21.3125 14.3877 21.3125 14.75C21.3125 15.1122 21.0192 15.4062 20.6562 15.4062Z" fill="#00FFF2" />
      <path d="M22.625 18.6875H16.0625C15.6996 18.6875 15.4062 18.3935 15.4062 18.0312C15.4062 17.669 15.6996 17.375 16.0625 17.375H22.625C22.9879 17.375 23.2812 17.669 23.2812 18.0312C23.2812 18.3935 22.9879 18.6875 22.625 18.6875Z" fill="#00FFF2" />
      <path d="M19.9081 21.3125H16.0625C15.6996 21.3125 15.4062 21.0185 15.4062 20.6562C15.4062 20.294 15.6996 20 16.0625 20H19.9081C20.271 20 20.5644 20.294 20.5644 20.6562C20.5644 21.0185 20.271 21.3125 19.9081 21.3125Z" fill="#00FFF2" />
      <path d="M18.3331 23.9375H16.0625C15.6996 23.9375 15.4062 23.6435 15.4062 23.2812C15.4062 22.919 15.6996 22.625 16.0625 22.625H18.3331C18.696 22.625 18.9894 22.919 18.9894 23.2812C18.9894 23.6435 18.696 23.9375 18.3331 23.9375Z" fill="#00FFF2" />
      <path d="M18.3331 27.875H15.4062C13.597 27.875 12.125 26.403 12.125 24.5938V13.4375C12.125 11.6282 13.597 10.1562 15.4062 10.1562H23.2812C25.0905 10.1562 26.5625 11.6282 26.5625 13.4375V18.8187C26.5625 19.181 26.2692 19.475 25.9062 19.475C25.5433 19.475 25.25 19.181 25.25 18.8187V13.4375C25.25 12.3521 24.3667 11.4688 23.2812 11.4688H15.4062C14.3208 11.4688 13.4375 12.3521 13.4375 13.4375V24.5938C13.4375 25.6792 14.3208 26.5625 15.4062 26.5625H18.3331C18.696 26.5625 18.9894 26.8565 18.9894 27.2188C18.9894 27.581 18.696 27.875 18.3331 27.875Z" fill="#00FFF2" />
      <path d="M24.5938 29.8438C22.0613 29.8438 20 27.7831 20 25.25C20 22.7169 22.0613 20.6562 24.5938 20.6562C27.1262 20.6562 29.1875 22.7169 29.1875 25.25C29.1875 27.7831 27.1262 29.8438 24.5938 29.8438ZM24.5938 21.9688C22.7845 21.9688 21.3125 23.4407 21.3125 25.25C21.3125 27.0593 22.7845 28.5312 24.5938 28.5312C26.403 28.5312 27.875 27.0593 27.875 25.25C27.875 23.4407 26.403 21.9688 24.5938 21.9688Z" fill="#00FFF2" />
      <path d="M24.1562 26.9123C23.9882 26.9123 23.8202 26.848 23.6922 26.72L22.8174 25.8453C22.5609 25.5887 22.5609 25.1739 22.8174 24.9173C23.074 24.6607 23.4888 24.6607 23.7454 24.9173L24.1562 25.3281L25.4424 24.0425C25.699 23.7859 26.1138 23.7859 26.3704 24.0425C26.627 24.2991 26.627 24.7145 26.3704 24.9705L24.6202 26.7207C24.4922 26.8487 24.3242 26.9123 24.1562 26.9123Z" fill="#00FFF2" />
    </svg>

  );

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={onClose}
      title="Check Result"
      headerIcon={headerIcon}
      size="md"
      width="462px"
      maxHeight="400px"
      className="check-result-modal"
    >
      <div className="flex flex-col h-full">
        {currentState === 'input' ? (
          // Initial State - Input Section
          <div className="flex-1 flex flex-col justify-center items-start gap-6 p-6">
            {/* QR Scanner Section */}
            <div className="flex items-center gap-6 w-full">
              {/* QR Scanner */}
              <div className="flex flex-col items-center gap-2">
                <div
                  className="w-[70px] h-[70px] bg-[#B0822A] rounded-lg flex items-center justify-center cursor-pointer relative overflow-hidden"
                  onClick={handleQRScannerClick}
                >
                  {isScanning ? (
                    <video
                      ref={videoRef}
                      className="w-full h-full object-cover"
                      autoPlay
                      muted
                      playsInline
                    />
                  ) : (
                    <svg width="42" height="42" viewBox="0 0 42 42" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-white">
                      <g clipPath="url(#clip0_822_4602)">
                        <path d="M2.32469 15.4948C1.19591 15.4948 0.443359 14.7423 0.443359 13.6135V6.08825C0.443359 2.89002 2.88904 0.444336 6.08727 0.444336H13.6125C14.7413 0.444336 15.4938 1.19688 15.4938 2.32567C15.4938 3.45445 14.7413 4.207 13.6125 4.207H6.08727C4.95849 4.207 4.20594 4.95954 4.20594 6.08833V13.6136C4.20602 14.7423 3.45347 15.4948 2.32469 15.4948Z" fill="white" />
                        <path d="M39.9505 15.4948C38.8217 15.4948 38.0692 14.7423 38.0692 13.6135V6.08825C38.0692 4.95947 37.3166 4.20692 36.1878 4.20692H28.6626C27.5338 4.20692 26.7812 3.45437 26.7812 2.32559C26.7812 1.19681 27.5338 0.444336 28.6626 0.444336H36.1878C39.3861 0.444336 41.8317 2.89002 41.8317 6.08825V13.6135C41.8318 14.7423 41.0793 15.4948 39.9505 15.4948Z" fill="white" />
                        <path d="M13.6125 41.8332H6.08727C2.88904 41.8332 0.443359 39.3875 0.443359 36.1893V28.664C0.443359 27.5353 1.19591 26.7827 2.32469 26.7827C3.45347 26.7827 4.20602 27.5353 4.20602 28.664V36.1893C4.20602 37.3181 4.95857 38.0706 6.08735 38.0706H13.6126C14.7414 38.0706 15.4939 38.8232 15.4939 39.952C15.4938 41.0807 14.7413 41.8332 13.6125 41.8332Z" fill="white" />
                        <path d="M36.1888 41.8333H28.6636C27.5348 41.8333 26.7822 41.0807 26.7822 39.952C26.7822 38.8232 27.5348 38.0706 28.6636 38.0706H36.1888C37.3176 38.0706 38.0701 37.3181 38.0701 36.1893V28.664C38.0701 27.5353 38.8227 26.7827 39.9515 26.7827C41.0802 26.7827 41.8328 27.5353 41.8328 28.664V36.1893C41.8327 39.3876 39.387 41.8333 36.1888 41.8333Z" fill="white" />
                        <path d="M17.3753 19.2574H9.85008C8.7213 19.2574 7.96875 18.5048 7.96875 17.3761V9.85081C7.96875 8.72203 8.7213 7.96948 9.85008 7.96948H17.3753C18.5041 7.96948 19.2567 8.72203 19.2567 9.85081V17.3761C19.2567 18.5048 18.5041 19.2574 17.3753 19.2574ZM11.7314 15.4947H15.4941V11.7321H11.7314V15.4947Z" fill="white" />
                        <path d="M17.3753 34.3079H9.85008C8.7213 34.3079 7.96875 33.5554 7.96875 32.4266V24.9013C7.96875 23.7726 8.7213 23.02 9.85008 23.02H17.3753C18.5041 23.02 19.2567 23.7726 19.2567 24.9013V32.4266C19.2567 33.5554 18.5041 34.3079 17.3753 34.3079ZM11.7314 30.5453H15.4941V26.7826H11.7314V30.5453Z" fill="white" />
                        <path d="M32.4261 19.2574H24.9009C23.7721 19.2574 23.0195 18.5048 23.0195 17.3761V9.85081C23.0195 8.72203 23.7721 7.96948 24.9009 7.96948H32.4261C33.5549 7.96948 34.3074 8.72203 34.3074 9.85081V17.3761C34.3074 18.5048 33.5549 19.2574 32.4261 19.2574ZM26.7822 15.4947H30.5449V11.7321H26.7822V15.4947Z" fill="white" />
                        <path d="M24.9009 28.664C23.7721 28.664 23.0195 27.9115 23.0195 26.7827V24.9013C23.0195 23.7726 23.7721 23.02 24.9009 23.02C26.0296 23.02 26.7822 23.7726 26.7822 24.9013V26.7827C26.7822 27.9115 26.0296 28.664 24.9009 28.664Z" fill="white" />
                        <path d="M32.4257 26.7827H30.5444C29.4156 26.7827 28.6631 26.0301 28.6631 24.9013C28.6631 23.7726 29.4156 23.02 30.5444 23.02H32.4257C33.5545 23.02 34.3071 23.7726 34.3071 24.9013C34.3071 26.0301 33.5545 26.7827 32.4257 26.7827Z" fill="white" />
                        <path d="M32.4256 34.3081H26.7817C25.6529 34.3081 24.9004 33.5555 24.9004 32.4267C24.9004 31.298 25.6529 30.5454 26.7817 30.5454H32.4256C33.5544 30.5454 34.307 31.298 34.307 32.4267C34.307 33.5555 33.5544 34.3081 32.4256 34.3081Z" fill="white" />
                      </g>
                      <defs>
                        <clipPath id="clip0_822_4602">
                          <rect width="42" height="42" fill="white" />
                        </clipPath>
                      </defs>
                    </svg>
                  )}
                </div>
                <span className="text-sm font-rubik text-white">Scan QR</span>
              </div>

              {/* OR Divider */}
              <div className="flex flex-col items-center gap-4">
                <div className="h-[20px] w-px bg-gray-600"></div>
                <span className="text-gray-400 font-rubik text-sm">OR</span>
                <div className="h-[20px] w-px bg-gray-600 "></div>
              </div>

              {/* Input Section */}
              <div className="flex flex-col gap-3 flex-1 w-full">
                <div className="flex flex-col gap-[8px]">
                  <label
                    className="font-rubik font-semibold text-white capitalize"
                    style={{ fontSize: '14px', fontWeight: 600 }}
                  >
                    Bet ID
                  </label>
                  <input
                    type="text"
                    className={`w-full h-[43px] bg-elevated  rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-400 focus:border-transparent `}
                    placeholder="Enter bet ID"
                    value={betId}
                    onChange={(e) => setBetId(e.target.value)}
                  />

                </div>
              </div>
            </div>

            <PrimaryButton
              onClick={handleSearch}
              loading={isLoading}
              size="lg"
              fullWidth
            >
              Search
            </PrimaryButton>

            {/* Error Display */}
            {error && (
              <div className="text-red-500 text-sm font-rubik text-center">
                {error}
              </div>
            )}
          </div>
        ) : (
          // Results State
          <div className="flex flex-col h-full">
            {/* Section 1 - Header */}
            <div className="p-4 border-b border-[#AF9660]">
              <div className="flex items-center justify-between">
                <div className="flex flex-col">
                  <span className="text-white font-rubik text-sm">
                    {betResult?.formattedDate} . {betResult?.formattedTime}
                  </span>
                  <span className="text-gray-400 font-rubik text-sm">
                    Bet ID: {betResult?.betId}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  {/* Share/Copy Buttons */}
                  <button
                    onClick={handleShare}
                    className="w-[78px] h-[46px] bg-[#333335] rounded border border-gray-600 text-white font-rubik text-sm"
                  >
                    Copy
                  </button>
                  <button
                    onClick={handleShare}
                    className="w-[78px] h-[46px] bg-[#333335] rounded border border-gray-600 text-white font-rubik text-sm"
                  >
                    Share
                  </button>
                  {/* Win/Loss Badge */}
                  <div className={`w-[78px] h-[46px] rounded flex items-center justify-center font-rubik font-semibold text-sm ${betResult?.status === 'win'
                    ? 'bg-[#00B800] text-white'
                    : betResult?.status === 'loss'
                      ? 'bg-red-600 text-white'
                      : 'bg-yellow-600 text-white'
                    }`}>
                    {betResult?.status === 'win' ? 'WIN' : betResult?.status === 'loss' ? 'LOSS' : betResult?.status?.toUpperCase()}
                  </div>
                </div>
              </div>
            </div>

            {/* Section 2 - Scrollable Bet List */}
            <div className="flex-1 overflow-y-auto p-4">
              {betResult?.betList.map((bet, index) => {
                // Parse marketName format
                const parts = bet.marketName.split(' -> ');
                const sport = parts[0] || '';
                const league = parts[1] || '';
                const match = parts[2] || '';
                const market = parts[3] || '';

                return (
                  <div key={index} className="flex justify-between items-start mb-4">
                    <div className="flex flex-col gap-1 flex-1">
                      <div className="text-[#7B7B7B] font-rubik font-normal text-sm leading-none">
                        ⚽ {match}
                      </div>
                      <div className="text-golden font-rubik font-medium text-lg leading-none capitalize">
                        {sport} {league}
                      </div>
                      <div className="text-white font-normal text-lg leading-none text-center">
                        {market}
                      </div>
                    </div>
                    <div className="text-golden font-rubik font-semibold text-xl leading-none text-center uppercase">
                      {bet.rate}
                    </div>
                  </div>
                );
              })}
            </div>

            {/* Section 3 - Fixed Bottom */}
            <div className="bg-[#FFFFFF1A] rounded-lg p-4 m-4 flex flex-col gap-3">
              <div className="flex justify-between items-center">
                <div className="flex flex-col">
                  <span className="text-gray-400 font-rubik text-sm">Bet Amount</span>
                  <span className="text-white font-rubik font-semibold text-lg">
                    LKR {betResult?.betAmount?.toLocaleString()}
                  </span>
                </div>
                <div className="flex flex-col text-right">
                  <span className="text-gray-400 font-rubik text-sm">
                    {betResult?.status === 'win' ? 'Winning Amount' : 'Losing Amount'}
                  </span>
                  <span className={`font-rubik font-semibold text-lg ${betResult?.status === 'win' ? 'text-green-500' : 'text-red-500'
                    }`}>
                    LKR {(betResult?.status === 'win' ? betResult?.winningAmount : betResult?.losingAmount)?.toLocaleString()}
                  </span>
                </div>
              </div>
              <PrimaryButton
                onClick={handleMarkAsSettled}
                loading={markAsSettledMutation.isPending}
                fullWidth
                className={betResult?.isSettled ? 'bg-green-600 hover:bg-green-700' : ''}
              >
                {betResult?.isSettled ? 'Settled' : 'Mark as Settled'}
              </PrimaryButton>
            </div>
          </div>
        )}
      </div>
    </BaseModal>
  );
};

export default CheckResultModal;
